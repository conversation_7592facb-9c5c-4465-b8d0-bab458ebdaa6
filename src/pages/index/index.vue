<template>
  <view class="index-container">
    <img class="bg-img" src="https://psm.bangdao-tech.com/interaction-putting/20316/img/20250522193550899099058243501272_w750_h1624.png">
    <my-navbar title="智能推广工具" type="normal" />

    <view class="header">
      <div class="header-left">
        <view class="avatar">
          <img :src="avatar" class="avatar-img">
        </view>
        <view class="welcome">{{ isLogin ? mobile : title }}</view>
      </div>
      <view class="header-right">
        <button open-type="getPhoneNumber" class="login-btn"  @getphonenumber="goToLogin">{{isLogin ? '切换账号' : '立即登录'}}</button>
      </view>
    </view>

    <menu-list :menuItems="menuItems" @item-click="onMenuItemClick" class="menu-list"  />
  </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import MenuList from '@/components/menu-list/menu-list.vue';
import {globalData} from '@/common/script/config';
import * as api from "@/service";

@Component({
  components: {
    MenuList
  },
  filters: {},
})
export default class Index extends Vue {
  menuItems = [
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250527114718833099058249200963_w304_h153.png',
      title: '预约登记',
      buttonText: '去登记',
      url: '/pages/booking/index'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250527114722852099058249601497_w304_h153.png',
      title: '预约攻略',
      buttonText: '去查看',
      url: '/pages/banner/index'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250527114726097099058249701427_w377_h153.png',
      title: '智能交费专区',
      buttonText: '去交费',
      url: '/pages/exchange/index'
    }
  ];

  avatar = 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250522195804239099058243600154_w56_h56.png';

  isLogin = false

  title = '欢迎使用智能推广工具';

  mobile = '173****3627'

  async onShow() {
    // 静默授权，获取token，判断是否过期，重新登录
    // 查询用户信息接口，有返回手机号无需授权，不返回手机号静默授权先获取一次，如果还没有返回手机号，展示登录按钮获取手机号授权弹窗
    const [err,res] = await uni.login({ provider: 'weixin' });
    console.log('code',res.code);
    const [ , loginRes] = await api.getAuth({
      scope:'snsapi_userinfo',
      code:res.code,
      ...globalData
    });
    console.log('loginRes',loginRes);

    if (loginRes.rtnCode === '000000'){
      const [,res] = await api.getAppInfo({
        ...globalData
      });
      if(res){
        const {rtn_data : { is_login ,book_guides , payment}} = res
        this.isLogin = is_login;
      }
    }

  }

  async getrealtimephonenumber (e) {
    console.log(e.detail.code)  // 动态令牌
    console.log(e.detail.errMsg) // 回调信息（成功失败都会返回）
    console.log(e.detail.errno)  // 错误码（失败时返回）
  }

  async goToLogin(e) {
    if (!e.detail.code) {
      uni.showToast({ title: '授权失败', icon: 'none' });
      return;
    }
    // 发送code到服务端解密
    const [,res] = await api.getLogin({
      app_id:'wxdb953d224b9ab455',
      pubms_code:'PWANHUI',
      source:'WECHAT',
      activity_id:'AH_FEE_CTRL_WECHAT_SHARE',
      code: e.detail.code
    });

    console.log("🚀 ~ file:index method:goToLogin line:108 -----", res)
  }

  onMenuItemClick({ item, index }) {
    console.log('点击了菜单项:', item, '索引:', index);
    // 可以在这里添加额外的点击处理逻辑
  }
}
</script>

<style lang="less" scoped>

.bg-img{
  height:1624upx;
}

.bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.header {
  position: absolute;
  top: 522upx;
  left: 24upx;
  display: flex;
  justify-content: space-between;
  background: url("https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523095441105099058244000387_w703_h158.png") no-repeat;
  background-size: 100% 100%;
  width: 703upx;
  height: 158upx;


  .header-left {
    //width: 316upx;
    height: 56upx;
    /* 自动布局 */
    display: flex;
    align-items: center;
    margin: 16rpx 0 0 24rpx;
    z-index: 0;
    .avatar-img {
      width: 56upx;
      height: 56upx;
    }
    .welcome {
      margin-left: 20upx;
      font-size: 24upx;
      font-weight: 500;
      line-height: 32upx;
      letter-spacing: normal;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .header-right {
    margin: 16upx 24upx 0 0;

    .login-btn {
      width: 146rpx;
      height: 51rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12upx 24upx;
      background: #21A3A1;
      color: #fff;
      border-radius: 30upx;
      font-size: 24upx;
    }
  }


}

.menu-list{
  position: absolute;
  top: 580upx;
  width: 100%;
}
</style>
