<template>
  <view class="index-container">
    <img class="bg-img" src="https://psm.bangdao-tech.com/interaction-putting/20316/img/20250522193550899099058243501272_w750_h1624.png">
    <my-navbar title="智能推广工具" type="normal" />




  </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

@Component({
  components: {
  },
  filters: {},
})
export default class Index extends Vue {


  async onShow() {
    // 页面显示时的逻辑
  }
}
</script>

<style lang="less" scoped>

.bg-img{
  height:1624upx;
}

.bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
</style>
