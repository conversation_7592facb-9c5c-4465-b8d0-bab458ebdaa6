<template>
  <view class="staff-index-container">
    <img
      class="bg-img"
      src="https://psm.bangdao-tech.com/interaction-putting/20316/img/20250522193550899099058243501272_w750_h1624.png"
    />
    <my-navbar title="员工工作台" type="normal" />

    <!-- 员工信息头部 -->
    <view class="staff-header">
      <view class="staff-info">
        <view class="avatar-section">
          <img :src="staffInfo.avatar" class="staff-avatar" />
          <view class="online-status"></view>
        </view>
        <view class="staff-details">
          <view class="staff-name">{{ staffInfo.name }}</view>
          <view class="staff-role">{{ staffInfo.role }}</view>
          <view class="staff-department">{{ staffInfo.department }}</view>
        </view>
      </view>
      <view class="staff-stats">
        <view class="stat-item">
          <view class="stat-number">{{ todayStats.tasks }}</view>
          <view class="stat-label">今日任务</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ todayStats.completed }}</view>
          <view class="stat-label">已完成</view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="section-title">快捷操作</view>
      <view class="action-grid">
        <view
          v-for="(action, index) in quickActions"
          :key="index"
          class="action-item"
          @click="handleQuickAction(action)"
        >
          <view class="action-icon">
            <img :src="action.icon" />
          </view>
          <view class="action-text">{{ action.title }}</view>
        </view>
      </view>
    </view>

    <!-- 工作模块 -->
    <view class="work-modules">
      <view class="section-title">工作模块</view>
      <menu-list :menuItems="workModules" @item-click="onModuleClick" />
    </view>

    <!-- 通知公告 -->
    <view class="announcements">
      <view class="section-title">
        <text>通知公告</text>
        <text class="more-link" @click="goToAnnouncements">更多</text>
      </view>
      <view class="announcement-list">
        <view
          v-for="(item, index) in announcements"
          :key="index"
          class="announcement-item"
          @click="viewAnnouncement(item)"
        >
          <view class="announcement-dot"></view>
          <view class="announcement-content">
            <view class="announcement-title">{{ item.title }}</view>
            <view class="announcement-time">{{ item.time }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import MenuList from '@/components/menu-list/menu-list.vue';

@Component({
  components: {
    MenuList,
  },
  filters: {},
})
export default class StaffIndex extends Vue {
  // 员工信息
  staffInfo = {
    name: '张小明',
    role: '推广专员',
    department: '市场推广部',
    avatar: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250522195804239099058243600154_w56_h56.png'
  };

  // 今日统计
  todayStats = {
    tasks: 8,
    completed: 5
  };

  // 快捷操作
  quickActions = [
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101856759099058244701383_w304_h153.png',
      title: '打卡签到',
      action: 'checkin'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101846790099058244200293_w304_h153.png',
      title: '任务上报',
      action: 'report'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101840839099058244000917_w377_h153.png',
      title: '客户拜访',
      action: 'visit'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101856759099058244701383_w304_h153.png',
      title: '数据统计',
      action: 'statistics'
    }
  ];

  // 工作模块
  workModules = [
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101856759099058244701383_w304_h153.png',
      title: '客户管理',
      buttonText: '进入',
      url: '/pages/customer/index'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101846790099058244200293_w304_h153.png',
      title: '推广活动',
      buttonText: '查看',
      url: '/pages/promotion/index'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101840839099058244000917_w377_h153.png',
      title: '业绩统计',
      buttonText: '查看',
      url: '/pages/performance/index'
    },
    {
      icon: 'https://psm.bangdao-tech.com/interaction-putting/20316/img/20250523101856759099058244701383_w304_h153.png',
      title: '培训学习',
      buttonText: '学习',
      url: '/pages/training/index'
    }
  ];

  // 通知公告
  announcements = [
    {
      id: 1,
      title: '关于开展春季推广活动的通知',
      time: '2024-03-15 09:30'
    },
    {
      id: 2,
      title: '新版客户管理系统上线公告',
      time: '2024-03-14 14:20'
    },
    {
      id: 3,
      title: '月度业绩考核标准调整通知',
      time: '2024-03-13 16:45'
    }
  ];

  async onShow() {
    // 页面显示时的逻辑
    await this.loadStaffInfo();
    await this.loadTodayStats();
  }

  // 加载员工信息
  async loadStaffInfo() {
    // TODO: 从API获取员工信息
    console.log('加载员工信息');
  }

  // 加载今日统计
  async loadTodayStats() {
    // TODO: 从API获取今日统计数据
    console.log('加载今日统计');
  }

  // 快捷操作点击
  handleQuickAction(action: any) {
    console.log('快捷操作:', action);
    switch (action.action) {
      case 'checkin':
        this.handleCheckin();
        break;
      case 'report':
        this.handleReport();
        break;
      case 'visit':
        this.handleVisit();
        break;
      case 'statistics':
        this.handleStatistics();
        break;
    }
  }

  // 打卡签到
  handleCheckin() {
    uni.showToast({
      title: '签到成功',
      icon: 'success'
    });
  }

  // 任务上报
  handleReport() {
    uni.navigateTo({
      url: '/pages/task/report'
    });
  }

  // 客户拜访
  handleVisit() {
    uni.navigateTo({
      url: '/pages/customer/visit'
    });
  }

  // 数据统计
  handleStatistics() {
    uni.navigateTo({
      url: '/pages/statistics/index'
    });
  }

  // 工作模块点击
  onModuleClick({ item, index }: any) {
    console.log('点击了工作模块:', item, '索引:', index);
  }

  // 查看更多公告
  goToAnnouncements() {
    uni.navigateTo({
      url: '/pages/announcements/index'
    });
  }

  // 查看公告详情
  viewAnnouncement(item: any) {
    uni.navigateTo({
      url: `/pages/announcements/detail?id=${item.id}`
    });
  }
}
</script>

<style lang="less" scoped>
.staff-index-container {
  min-height: 100vh;
  position: relative;
}

.bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

// 员工信息头部
.staff-header {
  margin: 30upx;
  padding: 40upx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20upx;
  box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);

  .staff-info {
    display: flex;
    align-items: center;
    margin-bottom: 30upx;

    .avatar-section {
      position: relative;
      margin-right: 30upx;

      .staff-avatar {
        width: 120upx;
        height: 120upx;
        border-radius: 50%;
        border: 4upx solid #21A3A1;
      }

      .online-status {
        position: absolute;
        bottom: 5upx;
        right: 5upx;
        width: 24upx;
        height: 24upx;
        background: #4cd964;
        border-radius: 50%;
        border: 3upx solid #fff;
      }
    }

    .staff-details {
      flex: 1;

      .staff-name {
        font-size: 36upx;
        font-weight: 600;
        color: #333;
        margin-bottom: 10upx;
      }

      .staff-role {
        font-size: 28upx;
        color: #21A3A1;
        margin-bottom: 8upx;
      }

      .staff-department {
        font-size: 24upx;
        color: #999;
      }
    }
  }

  .staff-stats {
    display: flex;
    justify-content: space-around;

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 48upx;
        font-weight: 600;
        color: #21A3A1;
        line-height: 1;
      }

      .stat-label {
        font-size: 24upx;
        color: #666;
        margin-top: 8upx;
      }
    }
  }
}

// 快捷操作
.quick-actions {
  margin: 0 30upx 30upx;

  .action-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20upx;
    margin-top: 20upx;

    .action-item {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 16upx;
      padding: 30upx 20upx;
      text-align: center;
      box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.05);

      .action-icon {
        margin-bottom: 16upx;

        img {
          width: 60upx;
          height: 60upx;
        }
      }

      .action-text {
        font-size: 24upx;
        color: #333;
      }
    }
  }
}

// 工作模块
.work-modules {
  margin: 0 0 30upx;
}

// 通知公告
.announcements {
  margin: 0 30upx 30upx;

  .announcement-list {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16upx;
    padding: 20upx;
    margin-top: 20upx;

    .announcement-item {
      display: flex;
      align-items: flex-start;
      padding: 20upx 0;
      border-bottom: 1upx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .announcement-dot {
        width: 12upx;
        height: 12upx;
        background: #21A3A1;
        border-radius: 50%;
        margin-top: 12upx;
        margin-right: 20upx;
        flex-shrink: 0;
      }

      .announcement-content {
        flex: 1;

        .announcement-title {
          font-size: 28upx;
          color: #333;
          line-height: 1.4;
          margin-bottom: 8upx;
        }

        .announcement-time {
          font-size: 24upx;
          color: #999;
        }
      }
    }
  }
}

// 通用样式
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32upx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20upx;

  .more-link {
    font-size: 26upx;
    color: #21A3A1;
    font-weight: normal;
  }
}
</style>
