import axios from 'axios-miniprogram';
import {SM3} from 'gm-crypto';
import store from '@/store/index';
import {globalConfig} from '@/common/script/config';
import {doDecrypt, doEncrypt} from '../utils/sm2';
import {Base64} from 'js-base64';
import {createUniAppAxiosAdapter} from '@uni-helper/axios-adapter';

export interface IResponse<T = any> {
  rtnData: T;
  rtnFlag: IRtnCode;
  rtnMsg: string;
}

// 业务返回code枚举
enum IRtnCode {
  SUCCESS = "9999", // 业务正常
  SYSTEM_UNKNOWN_ERROR = 'system-unknown-error', // 系统错误
  UNIFIED_ERROR_REPORTING = 'ext.api.invoke.wrong', // 系统统一报错
}

let requests: any = [];
/**
 * 异步等待对象的生成，对象生成完成返回生成的对象
 * @param getter 对象的获取函数
 * @param checkSize 检查粒度，ms
 * @param timeout 超时时间, ms
 */
export const asyncCheck = async <T>(getter: () => T, checkSize = 30, timeout = 10000) => {
  return new Promise<T>((x) => {
    const check = (num = 0) => {
      const target = getter();
      if (target !== undefined && target !== null) {
        x(target);
      } else if (num > timeout / checkSize) {
        // 超时
        x(target);
      } else {
        setTimeout(() => {
          // eslint-disable-next-line
          check((num += 1));
        }, checkSize);
      }
    };
    check();
  });
};

export function checkToken() {
  const {auth} = (store as any).state;
  if (auth.userInfo.authToken.length > 0) {
    return auth.userInfo.authToken;
  }
  return null;
}

const baseURL = globalConfig.baseUrl;
const axiosIns = axios.create({
  baseURL,
  timeout: 10000,
});

// request 请求拦截器1先走,保证静默授权优先
axiosIns.interceptors.request.use(
  (axiosConfig) => {
    const config = axiosConfig as any;
    const {auth} = (store as any).state;
    console.log('auth', auth, config);
    // const { systemInfo } = (store as any).state.system;

    config.headers = {
      // token: auth.userInfo.authToken,
      // Authorization: uni.getStorageSync('authToken'),
      cookie: uni.getStorageSync('authCookie'),


    };
    // todo
    /* process.env.NODE_ENV !== 'development' &&  */
    // TODO关闭加密
    // if (config.url !== '/file/upload' && config.method?.toLowerCase() === 'post') {
    //   const encryptedData = `04${doEncrypt(JSON.stringify(config.data))}`;

    //   config.data = {
    //     encryptData: encryptedData,
    //     sign: SM3.digest(encryptedData, 'utf8', 'hex'),
    //   };
    // }

    // if (systemInfo.app === 'mPaaS') {
    //   return config;
    // }

    if (auth.userInfo.authToken.length !== 0) {
      // 放通静默授权, 不检查token
      if (['/wechat/auth/silent', '/alipay/auth/silent'].includes(config.url)) {
        return config;
      }
      // 非静默授权,没有token的请求需要等待token返回,先放队列中,返回token后重发请求
      asyncCheck(checkToken).then((authToken) => {
        // console.log('等待返回的token', authToken);
        requests.forEach((resolve) => {
          resolve(authToken);
        });
        requests = [];
      });

      return new Promise((resolve) => {
        requests.push((authToken) => {
          // config.header.token = authToken;
          config.headers.Authorization = authToken;
          resolve(config);
        });
      });
    }
    console.log('拦截器1', {...config});
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);
//  response拦截器
axiosIns.interceptors.response.use(
  async (response: any) => {
    console.log('response----------', response?.cookies?.[0]?.match(/T=[^;]+/)?.[0]);
    const cookie = response?.cookies?.[0]?.match(/T=[^;]+/)?.[0] + ';' + response?.cookies?.[1]?.match(/birth=[^;]+/)?.[0]
    response?.headers?.['set-cookie'] && uni.setStorageSync('authCookie', cookie);
    // response?.headers?.['set-cookie'] && uni.setStorageSync('authCookie', response?.headers?.['set-cookie']);

    // todo
    const responseData = response.data as IResponse;
    // TODO关闭加密
    // responseData.rtnData = responseData.rtnData
    //   ? Base64.decode(doDecrypt(responseData.rtnData.substring(2)))
    //   : responseData.rtnData;

    // const responData = JSON.parse(Base64.decode(doDecrypt(response.data.encryptData.substring(2))));

    console.log('responseData', responseData);
    const {ret_code = "", content = {}} = responseData;
    const {rtn_flag, rtn_data, rtn_msg} = content;

    // console.log('responDataresponData', responData, response, rtnFlag)
    // 登录失效清空登录信息
    if ([response?.statusCode, rtn_flag].some((item) => ['401', 401].includes(item))) {
      uni.hideLoading();
      uni.removeStorageSync('authToken');
      uni.setStorageSync('isNagLogin', true);
      store.dispatch('auth/resetUserInfo');
      return;
    }
    console.log("🚀 ~ file:baseService method: line:154 -----", )
    // 正常流程
    if (rtn_flag === IRtnCode.SUCCESS) {
      // console.log('SUCCESS', rtnData)
      console.log("🚀 ~ file:baseService method: line:158 -----", )
      return Promise.resolve(responseData);
    } else {
      console.log("🚀 ~ file:baseService method: line:161 -----", )
      if (rtnFlag === IRtnCode.UNIFIED_ERROR_REPORTING) {
        await uni.showToast({
          title: rtn_msg,
          icon: 'none',
        });
      }
      // console.log('responData', responData)
      // 全局一级业务报错拦截处理
      return Promise.reject(responseData);
    }
  },
  (err) => {
    // console.log("错误兜底");
    return Promise.reject(err);
  },
);

/**
 * 封装get方法，对应get请求
 */
function get(
  url: string,
  params = {},
): Promise<[Error | IResponse | undefined | any, IResponse | undefined]> {
  return new Promise((resolve) => {
    axiosIns
      .get(url, params)
      .then((res) => {
        const response = res as any;
        resolve([undefined, response]);
      })
      .catch((err) => {
        if (err.rtnFlag === 3002) {
          console.log('err.rtnFlag', err.rtnFlag);
          // 令牌已过期
          uni.showToast({
            title: '登录失效请重新登录',
            icon: 'none',
          });
          uni.removeStorageSync('authToken');
          uni.removeStorageSync('port');
          uni.removeStorageSync('position');
          // 使用 reLaunch 跳转并刷新页面
          uni.reLaunch({url: '/pages/login/index'});
        }
        // console.warn("GET请求异常");
        resolve([err, undefined]);
      });
  });
}

/**
 * post方法，对应post请求
 */
function postP(url: string, data = {}): Promise<[Error | IResponse | undefined | any, any]> {
  return new Promise((resolve) => {
    axiosIns
      .post(url, data)
      .then((res) => {
        console.log('------------------', res);

        const response = res as any;
        // console.log('response', response)
        resolve([undefined, response]);
      })
      .catch((err) => {
        // console.log('POST请求异常', err);
        if (err.rtnFlag === 3002) {
          // 令牌已过期
          uni.showToast({
            title: '登录失效请重新登录',
            icon: 'none',
          });
          uni.removeStorageSync('authToken');
          uni.removeStorageSync('port');
          uni.removeStorageSync('position');
          /**
           *uni.reLaunch()
           *使用 reLaunch 跳转并刷新页面
           *跳转到新页面并清空页面栈，适合完全刷新页面。
           */
          /**
           * uni.redirectTo()：跳转到新页面并替换当前页面，适合需要刷新并跳转到新页面的场景。
           */
          uni.reLaunch({url: '/pages/login/index'});
        }
        resolve([err, undefined]);
      });
  });
}

/**
 * post方法，对应post请求
 */
function post(method: string, data = {}): Promise<[Error | IResponse | undefined | any, any]> {
  return new Promise((resolve) => {
    axiosIns
      .post('', {method, ...data})
      .then((res) => {
        console.log('------------------', res);

        const response = res as any;
        // console.log('response', response)
        resolve([undefined, response?.content]);
      })
      .catch((err) => {
        // console.log('POST请求异常', err);
        if (err.rtnFlag === 3002) {
          // 令牌已过期
          uni.showToast({
            title: '登录失效请重新登录',
            icon: 'none',
          });
          uni.removeStorageSync('authToken');
          uni.removeStorageSync('port');
          uni.removeStorageSync('position');
          /**
           *uni.reLaunch()
           *使用 reLaunch 跳转并刷新页面
           *跳转到新页面并清空页面栈，适合完全刷新页面。
           */
          /**
           * uni.redirectTo()：跳转到新页面并替换当前页面，适合需要刷新并跳转到新页面的场景。
           */
          uni.reLaunch({url: '/pages/login/index'});
        }
        resolve([err, undefined]);
      });
  });
}

/**
 * post方法，对应post请求
 */
function upload(url: string, data = {}): Promise<[Error | IResponse | undefined | any, any]> {
  return new Promise((resolve) => {
    axiosIns
      .post(url, data, {
        upload: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 1000 * 60,
      })
      .then((res) => {
        const response = res as any;
        // console.log('response', response)
        resolve([undefined, response]);
      })
      .catch((err) => {
        // console.log('POST请求异常', err);
        if (err.rtnFlag === 3002) {
          // 令牌已过期
          uni.showToast({
            title: '登录失效请重新登录',
            icon: 'none',
          });
          uni.removeStorageSync('authToken');
          uni.removeStorageSync('port');
          uni.removeStorageSync('position');
          /**
           *uni.reLaunch()
           *使用 reLaunch 跳转并刷新页面
           *跳转到新页面并清空页面栈，适合完全刷新页面。
           */
          /**
           * uni.redirectTo()：跳转到新页面并替换当前页面，适合需要刷新并跳转到新页面的场景。
           */
          uni.reLaunch({url: '/pages/login/index'});
        }
        resolve([err, undefined]);
      });
  });
}


export {get, post, upload, postP};
