import { post, upload,postP } from '@/service/baseService';
import { postToken } from '@/service/requestToken';
// 基础信息查询
export function getAuth(params:any) {
  return postToken('bangdao.user.share.weixinapp', params);
}
// 登录
export function getLogin(params:any) {
  return post('bangdao.lifecycle.feectrl.pwanhui.app.login', params);
}
// 获取首页信息
export function getAppInfo(params:any) {
  return post('bangdao.lifecycle.feectrl.pwanhui.app.homePage', params);
}
