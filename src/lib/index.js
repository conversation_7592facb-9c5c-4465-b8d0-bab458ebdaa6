import { ENCRYPT_REQ_PARAMS_PUBLIC_KEY, DECRYPT_RESPONSE_PRIVITE_KEY } from "./encrypt-constant";
// import { JSEncrypt } from './jsencrypt'
import { _encode_, _decode_ } from './base64'

// const encodeJsencrypt = new JSEncrypt();
// encodeJsencrypt.setPublicKey(ENCRYPT_REQ_PARAMS_PUBLIC_KEY);

// const decodeJsencrypt = new JSEncrypt();
// decodeJsencrypt.setPrivateKey(DECRYPT_RESPONSE_PRIVITE_KEY);

// function encryptLongString(str) {
//   let newStr = '';
//   if (str.length > 117) {
//     const arr = str.match(/.{1,117}/g);
//     arr.forEach(el => {
//       const elStr = encodeJsencrypt.encrypt(el);
//       newStr += elStr;
//     });
//     return newStr;
//   } else {
//     return encodeJsencrypt.encrypt(str);
//   }
// }

// 网关接口RSA加密入参处理
export const encyptData =  params => {
  let dataParams;
  if (Object(params).length <= 0) {
    dataParams = '';
  } else {
    dataParams = JSON.stringify(params) + '';
  }
  console.log(dataParams);
  return {
    bdgatewayafterencryption: _encode_(dataParams),
    // bdgatewayafterencryption: encryptLongString(dataParams),
  };
};


// 网关接口RSA加密入参处理
export const decyptData = encodeStr => {
  if (!encodeStr) {
    console.log('接口没有返回');
    return false;
  }
  // let newStr = '';
  // if (encodeStr.length > 344) {
  //   const arr = encodeStr.match(/.{1,344}/g);
  //   arr.forEach(el => {
  //     const elStr = decodeJsencrypt.decrypt(el);
  //     newStr += elStr;
  //   });
  // } else {
  //   newStr = decodeJsencrypt.decrypt(encodeStr);
  // }
  try {
    if (typeof encodeStr === 'string') {
      return JSON.parse(_decode_(encodeStr));
    } else {
      return encodeStr
    }
    // return JSON.parse(Base64.decode(newStr));
  } catch (error) {
    console.error('JSON parse Error in decrypt response', error);
    return encodeStr;
  }
};



// 网关接口入参RSA加密类型
export const encryptHeader = {
  bdgatewayencryptiontype: 'bd66', // base64
  // bdgatewayencryptiontype: 'rsa',
};
// 网关接口出参RSA加密类型
export const decryptHeader = {
  bdgatewayresponseneedencryptiontype: 'bd66', // base64
  // bdgatewayresponseneedencryptiontype: 'rsa',
};
