---
description: 
globs: 
alwaysApply: false
---
# MasterGo设计稿转移动端代码规范指南

## 设计稿解析与准备

### 设计稿分析
- 使用`mcp__getDsl`工具获取MasterGo设计稿的DSL数据
- 分析设计稿的层级结构、组件关系和交互逻辑
- 识别可复用组件和设计模式
- 提取设计规范（颜色、字体、间距等）

### 图片资源处理
- 从`mcp__getDsl`获取的DSL数据中提取图片URL
  - 图片URL通常存在于以下位置：
    - `"paint_xxx":{"value":[{"url":"https://image-resource.mastergo.com/..."}]}`
    - `fills`属性中的`image.url`字段
    - `backgroundImage`字段
- 使用Cursor内置的下载工具保存图片到本地`static`目录
  ```bash
  # 简单的图片下载命令
  mkdir -p static/images
  curl -o static/images/图片名称.png "https://image-resource.mastergo.com/图片路径.png"
  ```
- 按照功能和用途分类存储图片
  - `static/icons`: 存放图标类资源
  - `static/images`: 存放内容图片
  - `static/backgrounds`: 存放背景图片
- 在组件中使用本地图片资源路径替代远程URL

## 代码实现规范

### 项目结构
- 按功能模块组织代码结构
- 将可复用组件放在`components`目录下
- 将页面级组件放在`pages`目录下
- 将公共样式、工具函数等放在相应的目录中

### 组件设计
- 遵循单一职责原则，每个组件只负责一个功能
- 将复杂组件拆分为多个小组件
- 使用props传递数据，使用事件传递行为
- 为组件添加适当的注释和文档

### 样式实现
- 使用SCSS/LESS等预处理器提高样式可维护性
- 采用BEM命名规范避免样式冲突
- 使用变量存储颜色、字体等设计规范
- 实现响应式布局，适配不同屏幕尺寸

### 响应式布局
- 使用相对单位（rpx、vw、vh等）代替固定像素
- 实现弹性布局，使用flex或grid布局
- 设置适当的媒体查询断点
- 考虑不同设备的特性（如安全区域、刘海屏等）

## 移动端特性适配

### 性能优化
- 减少DOM操作和重绘
- 优化图片和资源加载
- 使用懒加载和虚拟列表优化长列表
- 减少不必要的网络请求

### 交互体验
- 实现合适的触摸反馈
- 优化表单输入体验
- 考虑手势操作和滑动效果
- 优化页面过渡动画

### 兼容性处理
- 测试不同设备和系统版本
- 处理不同屏幕尺寸和分辨率
- 解决常见的移动端兼容性问题
- 提供降级方案

## 图片资源处理工作流

### 从DSL提取图片
```javascript
// 从DSL数据中提取图片URL的简单函数
function extractImageUrls(dslData) {
  const imageUrls = new Set();
  
  // 遍历所有paint属性
  for (const key in dslData) {
    if (key.startsWith('paint_') && dslData[key].value) {
      dslData[key].value.forEach(item => {
        if (item.url) {
          imageUrls.add(item.url);
        }
      });
    }
  }
  
  return Array.from(imageUrls);
}

// 使用示例
const imageUrls = extractImageUrls(dslData);
console.log('找到图片URL:', imageUrls);
```

## 最佳实践示例

### 图片资源使用示例
```vue
<!-- 组件中使用本地图片资源的示例 -->
<template>
  <view class="product-card">
    <image src="/static/images/product.png" class="product-card__image"></image>
    <view class="product-card__content">
      <text class="product-card__title">产品名称</text>
      <image src="/static/icons/cart-icon.png" class="product-card__cart-icon"></image>
    </view>
  </view>
</template>

<style lang="scss">
.product-card {
  width: 340rpx;
  border-radius: 16rpx;
  overflow: hidden;
  
  &__image {
    width: 100%;
    height: 340rpx;
  }
  
  &__content {
    padding: 20rpx;
  }
  
  &__title {
    font-size: 28rpx;
    font-weight: 500;
  }
  
  &__cart-icon {
    width: 48rpx;
    height: 48rpx;
  }
}
</style>
```

### 组件封装示例
```