---
description: 
globs: 
alwaysApply: false
---
# 小朗充电项目技术栈说明

## 核心技术框架

### 前端框架
- **Vue 2**：采用Vue2作为核心前端框架，利用其响应式系统和组合式API
- **TypeScript**：使用TypeScript提供类型检查和代码提示
- **Uni-App**：跨平台开发框架，支持一次开发多端部署

### 样式处理
- **Sass/SCSS**：使用Sass预处理器增强CSS功能
- **BEM命名规范**：采用Block-Element-Modifier命名方法组织CSS
- **rpx单位**：使用rpx作为主要尺寸单位，实现屏幕自适应

### 构建工具
- **vue**：现代前端构建工具，提供快速的开发体验
- **Uni-App CLI**：uni-app命令行工具，用于项目构建和部署

## 项目架构

### 目录结构
- **src/pages**：存放页面组件
- **src/components**：存放可复用组件
  - **common**：通用UI组件
  - **business**：业务组件
- **src/static**：存放静态资源（图片、图标等）
- **src/utils**：工具函数和辅助方法
- **src/store**：状态管理
- **src/api**：API接口封装

### 多端支持
- 微信小程序

## 开发规范

### 组件开发
- 使用组合式API (Composition API)
- 组件文件使用PascalCase命名
- 组件属性和事件使用TypeScript类型定义

### 样式规范
- 使用SCSS编写样式
- 组件样式使用scoped或module隔离
- 使用变量管理颜色、字体等设计标准

### 代码质量
- TypeScript类型检查
- Vue单文件组件结构一致性
- 代码格式化和lint规则

## 设计稿转代码流程

1. 使用MasterGo工具获取设计稿DSL数据
2. 分析组件结构和样式规范
3. 提取静态资源并优化
4. 实现基础组件和布局
5. 开发业务组件和页面
6. 实现响应式适配
7. 测试和优化

## 性能优化策略

- 组件懒加载
- 图片资源优化
- 减少不必要的渲染
- 合理使用缓存
- 网络请求优化